import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Provider as PaperProvider } from 'react-native-paper';
import AudioRecorder from '../AudioRecorder';

// Mock react-native-audio-recorder-player
jest.mock('react-native-audio-recorder-player', () => {
  return jest.fn().mockImplementation(() => ({
    startRecorder: jest.fn().mockResolvedValue('mock-path'),
    stopRecorder: jest.fn().mockResolvedValue('mock-result'),
    startPlayer: jest.fn().mockResolvedValue('mock-player'),
    stopPlayer: jest.fn().mockResolvedValue('mock-stop'),
    addRecordBackListener: jest.fn(),
    removeRecordBackListener: jest.fn(),
    addPlayBackListener: jest.fn(),
    removePlayBackListener: jest.fn(),
    mmss: jest.fn().mockReturnValue('00:00'),
  }));
});

// Mock @gorhom/bottom-sheet
jest.mock('@gorhom/bottom-sheet', () => ({
  BottomSheetModal: ({ children }) => children,
  BottomSheetView: ({ children }) => children,
  BottomSheetBackdrop: () => null,
}));

// Mock API
jest.mock('../../api/uploadFile', () => ({
  uploadFile: jest.fn().mockResolvedValue({ STATUS: 0 }),
}));

const defaultProps = {
  visible: true,
  uploadUri: 'https://example.com/upload',
  onClose: jest.fn(),
  recordedUri: '',
  filePrefix: 'test',
  helperText: '测试帮助文本',
  transcribedText: '测试识别文本',
};

const renderWithProvider = (component) => {
  return render(
    <PaperProvider>
      {component}
    </PaperProvider>
  );
};

describe('AudioRecorder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render in Record state when recordedUri is empty', () => {
    const { getByText } = renderWithProvider(
      <AudioRecorder {...defaultProps} recordedUri="" />
    );

    expect(getByText('录音')).toBeTruthy();
  });

  test('should render in Play state when recordedUri is provided', () => {
    const { getByText } = renderWithProvider(
      <AudioRecorder {...defaultProps} recordedUri="https://example.com/audio.mp3" />
    );

    expect(getByText('播放')).toBeTruthy();
    expect(getByText('识别结果')).toBeTruthy();
    expect(getByText('重新录制')).toBeTruthy();
  });

  test('should show transcription dialog when clicking 识别结果', () => {
    const { getByText } = renderWithProvider(
      <AudioRecorder
        {...defaultProps}
        recordedUri="https://example.com/audio.mp3"
        transcribedText="这是测试识别文本"
      />
    );

    fireEvent.press(getByText('识别结果'));
    expect(getByText('语音识别结果')).toBeTruthy();
    expect(getByText('这是测试识别文本')).toBeTruthy();
  });

  test('should switch to Record state when clicking 重新录制', () => {
    const { getByText } = renderWithProvider(
      <AudioRecorder {...defaultProps} recordedUri="https://example.com/audio.mp3" />
    );

    fireEvent.press(getByText('重新录制'));
    // After clicking, it should switch to Record state
    // Note: This test might need adjustment based on actual state management
  });

  test('should update state when recordedUri prop changes', () => {
    const { rerender, getByText } = renderWithProvider(
      <AudioRecorder {...defaultProps} recordedUri="" />
    );

    // Initially should be in Record state
    expect(getByText('录音')).toBeTruthy();

    // Update recordedUri prop
    rerender(
      <PaperProvider>
        <AudioRecorder {...defaultProps} recordedUri="https://example.com/audio.mp3" />
      </PaperProvider>
    );

    // Should now be in Play state
    expect(getByText('播放')).toBeTruthy();
    expect(getByText('识别结果')).toBeTruthy();
    expect(getByText('重新录制')).toBeTruthy();
  });

  test('should prevent rapid clicking on play button', async () => {
    jest.useFakeTimers();

    const { getByRole } = renderWithProvider(
      <AudioRecorder {...defaultProps} recordedUrl="https://example.com/audio.mp3" />
    );

    // Find the play button (IconButton with play icon)
    const playButton = getByRole('button');

    // Simulate rapid clicking
    fireEvent.press(playButton);
    fireEvent.press(playButton);
    fireEvent.press(playButton);

    // Fast forward time to simulate debounce
    jest.advanceTimersByTime(300);

    // The function should only be called once due to debouncing
    // Note: This test might need adjustment based on actual implementation

    jest.useRealTimers();
  });

  test('should display correct colors for different states', () => {
    // This test would need to check the background colors
    // Implementation depends on how you want to test styling
  });
});
