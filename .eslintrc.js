module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true,
    },
    extends: [
        "eslint:recommended",
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "prettier", //确保 'prettier' 是 extends 数组中的最后一个元素
    ],
    overrides: [
        {
            env: {
                node: true,
            },
            files: [".eslintrc.{js,cjs}"],
            parserOptions: {
                sourceType: "script",
            },
        },
    ],
    parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
    },
    plugins: ["react", "react-hooks"],
    rules: {
        //indent: ["error", 4, { SwitchCase: 1 }],
        "linebreak-style": ["error", "windows"],
        quotes: ["error", "double", { avoidEscape: true }],
        semi: ["error", "always"],
        "object-curly-spacing": ["error", "always"],
        "no-unused-vars": [
            "error",
            {
                args: "none",
                argsIgnorePattern: "^_",
                varsIgnorePattern: "^_|^React$", // 忽略对'React'变量的检查
            },
        ], // allow unused vars in the functions
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",
        "react/prop-types": "off",
    },
    settings: {
        react: {
            version: "detect",
        },
    },
};
