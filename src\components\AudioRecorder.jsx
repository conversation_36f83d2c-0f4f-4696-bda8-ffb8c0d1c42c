import { BottomSheetBackdrop, BottomSheetModal, BottomSheetView } from "@gorhom/bottom-sheet";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Alert, PermissionsAndroid, Platform, StyleSheet, View } from "react-native";
import AudioRecorderPlayer, {
    AudioEncoderAndroidType,
    AudioSourceAndroidType,
    AVEncoderAudioQualityIOSType,
    OutputFormatAndroidType,
} from "react-native-audio-recorder-player";
import { Button, Dialog, IconButton, Portal, Text } from "react-native-paper";
import { uploadFile } from "../api/uploadFile";
import { DialogToConfirm } from "./DialogToConfirm"; // 导入DialogToConfirm组件


// 自定义的 Backdrop 组件
const CustomBottomSheetBackdrop = (props) => {
  return (
    <BottomSheetBackdrop
      {...props}
      pressBehavior="none" // 设置为 'none' 后，点击背景将不会有任何响应
      // 也可以调整其他属性，例如出现的动画效果
      appearsOnIndex={0}
      disappearsOnIndex={-1}
    />
  );
};

/**
 * 录音组件
 * https://github.com/hyochan/react-native-audio-recorder-player
 * @param {object} args
 * @param {object} args.visible
 * @param {string} args.uploadUri
 * @param {function} args.onClose
 * @param {string} args.filePrefix 保存到服务器上的文件名前缀, 格式为"${filePrefix}_${timestamp}"
 * @returns
 */
const AudioRecorder = ({ visible, uploadUri, onClose, recordedUrl = "", filePrefix = "recording", helperText = "请一次性采访完所有内容.", transcribedText= "", updateTranscribedTextCallback }) => {
  // 根据recordedUrl参数决定初始状态
  const getInitialState = () => {
    return recordedUrl ? "Play" : "Record";
  };

  const [recordingState, setRecordingState] = useState(getInitialState()); // "Record", "OnRecording", "Recorded", "Play"
  const [recordTime, setRecordTime] = useState("00:00");
  const [recordPath, setRecordPath] = useState("");
  const [isPlaying, setIsPlaying] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [recordDuration, setRecordDuration] = useState(0); // 保存录音总时长
  const [showHelpDialog, setShowHelpDialog] = useState(false); // 控制帮助对话框显示状态
  const [showTranscriptionDialog, setShowTranscriptionDialog] = useState(false); // 控制识别结果对话框显示状态
  const [playDuration, setPlayDuration] = useState(0); // 保存远程音频总时长

  const bottomSheetModalRef = useRef(null);
  const recordingTimer = useRef(null);
  const startTime = useRef(0);
  const isOperating = useRef(false); // 防止重复操作的标志
  const lastClickTime = useRef(0); // 防抖：记录上次点击时间

  // BottomSheetModal snap points - 调整为更小的高度
  const snapPoints = ["30%"]; // 第一个是初始高度, 可向数组添加其它高度使得bottomsheet的高度可以手动调整

  // 配置参数位于"node_modules/react-native-audio-recorder-player/src/AudioRecorderPlayer.nitro.ts"
  const audioSet = {
    // iOS Settings
    AVSampleRateKeyIOS: 44100,
    AVFormatIDKeyIOS: "aac", // AVEncodingOption导入失败, 使用具体值
    AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
    AVNumberOfChannelsKeyIOS: 2,
    // Android Settings
    AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
    AudioSourceAndroid: AudioSourceAndroidType.MIC,
    OutputFormatAndroid: OutputFormatAndroidType.MPEG_4,
    // Common settings
    AudioQuality: "high",
    AudioEncodingBitRate: 96000, // 比特率, 64000~128000, 较低的比特率可以减小文件大小
    AudioSamplingRate: 16000, // 采样率, 16kHz
  };

  const meteringEnabled = true; // Enable audio metering

  // 当组件挂载时自动展开 BottomSheetModal
  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
    }
  }, [visible]);

  // 监听recordedUrl变化，动态更新状态
  useEffect(() => {
    const newState = recordedUrl ? "Play" : "Record";
    console.log("......recordedUrl changed, new state should be:", newState);
    setRecordingState(newState);
  }, [recordedUrl]);

  // 处理Play状态的初始化
  useEffect(() => {
    if (recordingState === "Play" && recordedUrl) {
      // 重置播放相关状态
      setIsPlaying(false);
      setRecordTime("00:00");
      setPlayDuration(0);
    }
  }, [recordingState, recordedUrl]);

  useEffect(() => {
    return () => {
      // Cleanup on unmount - 确保清理所有音频播放资源
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      AudioRecorderPlayer.stopRecorder().catch(console.error);
      AudioRecorderPlayer.stopPlayer().catch(console.error);
      AudioRecorderPlayer.removeRecordBackListener();
      AudioRecorderPlayer.removePlayBackListener();
    };
  }, []);

  const formatTime = (milliseconds) => {
    return AudioRecorderPlayer.mmss(Math.floor(milliseconds / 1000));
  };

  const openAppSettings = () => {
    const { Linking } = require("react-native");
    if (Platform.OS === "android") {
      Linking.openSettings();
    } else {
      Linking.openURL("app-settings:");
    }
  };

  const checkPermissions = async () => {
    if (Platform.OS === "android") {
      if(Platform.Version < 13) {
        try {
            const grants = await PermissionsAndroid.requestMultiple([
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
            PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
            ]);

            if ( grants["android.permission.WRITE_EXTERNAL_STORAGE"] === PermissionsAndroid.RESULTS.GRANTED &&
                 grants["android.permission.READ_EXTERNAL_STORAGE"] === PermissionsAndroid.RESULTS.GRANTED &&
                 grants["android.permission.RECORD_AUDIO"] === PermissionsAndroid.RESULTS.GRANTED) {
                return true;
            } else {
                Alert.alert(
                    "权限不足",
                    "需要录音和存储权限才能使用录音功能",
                    [ { text: "确定", onPress: openAppSettings } ]
                );
                return false;
            }
        } catch (err) {
            console.warn(err);
            return false;
        }
      } else { // android 13+
          try {
            const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
            {
                title: "权限不足",
                message: "需要录音权限才能使用录音功能",
                buttonNeutral: "稍后询问",
                buttonNegative: "取消",
                buttonPositive: "确定",
            }
            );

            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                console.log("Recording permission granted");
            } else {
                console.log("Recording permission denied");
            return;
            }
        } catch (err) {
            console.warn(err);
            return;
        }
      }
    }
    return true; // iOS permissions are handled in Info.plist
  };

  const startRecording = async () => {
    setIsLoading(true);
    try {
      // Check permissions first
      const hasPermissions = await checkPermissions();
      if (!hasPermissions) {
        return;
      }

      // Set up recording progress listener first
      AudioRecorderPlayer.addRecordBackListener((e) => {
        setRecordTime(formatTime(e.currentPosition));
      });

      const path = await AudioRecorderPlayer.startRecorder(
        undefined, // Use default path
        audioSet,
        meteringEnabled
      );
      setRecordPath(path);
      setRecordingState("OnRecording");
      startTime.current = Date.now();
    } catch (error) {
      console.error("Failed to start recording:", error);
      Alert.alert("错误", "录音启动失败");
    } finally {
      setIsLoading(false);
    }
  };

  const stopRecording = async () => {
    setIsLoading(true);
    try {
      const result = await AudioRecorderPlayer.stopRecorder();
      AudioRecorderPlayer.removeRecordBackListener();

      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      // 计算录音时长（毫秒）
      const duration = Date.now() - startTime.current;
      setRecordDuration(duration);

      // 设置显示的时间为录音总时长
      setRecordTime(formatTime(duration));
      setRecordingState("Recorded");
      console.log("Recording stopped:", result);
    } catch (error) {
      console.error("Failed to stop recording:", error);
      Alert.alert("错误", "录音停止失败");
    } finally {
      setIsLoading(false);
    }
  };

  const playRecording = async () => {
    // 防抖：防止快速重复点击（300ms内的重复点击将被忽略）
    const now = Date.now();
    if (now - lastClickTime.current < 300) return;
    lastClickTime.current = now;

    // 防止重复点击和并发操作
    if (isLoading || isOperating.current) return;

    isOperating.current = true;
    setIsLoading(true);
    try {
      if (isPlaying) {
        // 停止播放
        await AudioRecorderPlayer.stopPlayer();
        AudioRecorderPlayer.removePlayBackListener();
        setIsPlaying(false);
        // 恢复显示录音总时长
        setRecordTime(formatTime(recordDuration));
      } else {
        // 开始播放前先确保清理之前的监听器
        AudioRecorderPlayer.removePlayBackListener();

        await AudioRecorderPlayer.startPlayer(recordPath);
        setIsPlaying(true);

        AudioRecorderPlayer.addPlayBackListener((e) => {
          // 播放期间更新计时器显示播放进度
          setRecordTime(formatTime(e.currentPosition));

          // 播放完成时的处理
          if (e.currentPosition >= e.duration && e.duration > 0) {
            setIsPlaying(false);
            AudioRecorderPlayer.removePlayBackListener();
            // 播放完成后恢复显示录音总时长
            setRecordTime(formatTime(recordDuration));
          }
        });
      }
    } catch (error) {
      console.error("Failed to play recording:", error);
      Alert.alert("错误", "播放失败");
      // 发生错误时重置状态
      setIsPlaying(false);
      AudioRecorderPlayer.removePlayBackListener();
    } finally {
      setIsLoading(false);
      isOperating.current = false;
    }
  };

  // 播放远程音频文件
  const playRemoteAudio = async () => {
    // 防抖：防止快速重复点击（300ms内的重复点击将被忽略）
    const now = Date.now();
    if (now - lastClickTime.current < 300) return;
    lastClickTime.current = now;

    // 防止重复点击和并发操作
    if (isLoading || isOperating.current) return;

    isOperating.current = true;
    setIsLoading(true);
    try {
      if (isPlaying) {
        // 停止播放
        await AudioRecorderPlayer.stopPlayer();
        AudioRecorderPlayer.removePlayBackListener();
        setIsPlaying(false);
        // 恢复显示播放总时长
        setRecordTime(formatTime(playDuration));
      } else {
        // 开始播放前先确保清理之前的监听器
        AudioRecorderPlayer.removePlayBackListener();

        await AudioRecorderPlayer.startPlayer(recordedUrl);
        setIsPlaying(true);

        AudioRecorderPlayer.addPlayBackListener((e) => {
          // 播放期间更新计时器显示播放进度
          setRecordTime(formatTime(e.currentPosition));

          // 保存音频总时长
          if (e.duration > 0 && playDuration === 0) {
            setPlayDuration(e.duration);
          }

          // 播放完成时的处理
          if (e.currentPosition >= e.duration && e.duration > 0) {
            setIsPlaying(false);
            AudioRecorderPlayer.removePlayBackListener();
            // 播放完成后恢复显示播放总时长
            setRecordTime(formatTime(e.duration));
          }
        });
      }
    } catch (error) {
      console.error("Failed to play remote audio:", error);
      Alert.alert("错误", "播放远程音频失败");
      // 发生错误时重置状态
      setIsPlaying(false);
      AudioRecorderPlayer.removePlayBackListener();
    } finally {
      setIsLoading(false);
      isOperating.current = false;
    }
  };

  const deleteRecording = () => {
    Alert.alert(
      "删除录音",
      "确定要删除这个录音吗？",
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: () => {
            setRecordingState("Record");
            setRecordTime("00:00");
            setRecordPath("");
            setRecordDuration(0);
            setIsPlaying(false);
          }
        }
      ]
    );
  };

  const uploadRecording = async () => {
    if (!uploadUri || !recordPath) {
      Alert.alert("错误", "上传路径或录音文件不存在");
      return;
    }

    try {
      const fileName = `${filePrefix}_${Date.now()}`;
      //console.log("本地录音文件:", recordPath, fileName, uploadUri);
      // recordPath: /data/user/0/com.lowcarbonmanagement.debug/files/sound_1752649378580.mp4
      const response = await uploadFile(recordPath, fileName, uploadUri);

      if (response && response.STATUS === 0) {
        setShowUploadDialog(true);
      } else {
        Alert.alert("上传失败", "录音上传失败，请重试");
      }
    } catch (error) {
      console.error("Upload failed:", error);
      Alert.alert("上传失败", "录音上传失败，请重试");
    }
  };

  const handleUploadDialogConfirm = () => {
    setShowUploadDialog(false);
    setRecordingState("Record");
    setRecordTime("00:00");
    setRecordPath("");
    setRecordDuration(0);
    setIsPlaying(false);
  };

  const handleClose = () => {
    if (recordingState === "OnRecording") {
      stopRecording();
    }
    // 如果正在播放，停止播放
    if (isPlaying) {
      AudioRecorderPlayer.stopPlayer().catch(console.error);
      AudioRecorderPlayer.removePlayBackListener();
      setIsPlaying(false);
    }
    bottomSheetModalRef.current?.dismiss();
    onClose();
  };

  const getStateColor = () => {
    switch (recordingState) {
      case "Record":
        return "#a8a9f2ff"; // Blue
      case "OnRecording":
        return "#f89595ff"; // Red
      case "Recorded":
        return "#6b7280"; // Gray
      case "Play":
        return "#10b981"; // Green
      default:
        return "#6366f1";
    }
  };

  const renderRecordButton = () => {
    switch (recordingState) {
      case "Record":
        return (
          <IconButton
            icon="microphone"
            size={40}
            iconColor="#6366f1"
            containerColor="white"
            onPress={startRecording}
            disabled={isLoading}
            style={styles.actionButton}
          />
        );
      case "OnRecording":
        return (
          <IconButton
            icon="stop"
            size={40}
            iconColor="#ef4444"
            containerColor="white"
            onPress={stopRecording}
            disabled={isLoading}
            style={styles.actionButton}
          />
        );
      case "Recorded":
        return (
          <IconButton
            icon={isPlaying ? "pause" : "play"}
            size={40}
            iconColor="#444851ff"
            containerColor="white"
            onPress={playRecording}
            disabled={isLoading}
            style={styles.actionButton}
          />
        );
      case "Play":
        return (
          <IconButton
            icon={isPlaying ? "pause" : "play"}
            size={40}
            iconColor="#10b981"
            containerColor="white"
            onPress={playRemoteAudio}
            disabled={isLoading}
            style={styles.actionButton}
          />
        );
      default:
        return null;
    }
  };

  const getSheetTitle = useCallback(() => {
    if (recordingState === "OnRecording") {
      return "录音中...";
    } else if (recordingState === "Recorded") {
      return "已录制";
    } else if (recordingState === "Play") {
      return "播放";
    } else {
      return "录音";
    }
  }, [recordingState]);

  // 只在 visible 为 true 时渲染 BottomSheetModal
  if (!visible) {
    return null;
  }

  console.log("...........recordingState: ", recordingState);

  return (
    <>
        <BottomSheetModal
            ref={bottomSheetModalRef}
            index={0}
            snapPoints={snapPoints}
            enablePanDownToClose={false}
            enableOverDrag={false}
            onDismiss={handleClose}
            backgroundStyle={styles.modalBackground}
            backdropComponent={CustomBottomSheetBackdrop}
            handleComponent={null} // 不显示顶部的handle(或称为Handle Indicator)
        >
            <BottomSheetView style={[styles.container, { backgroundColor: getStateColor() }]}>
                <View style={styles.header}>
                    <View style={styles.headerLeft}>
                        <IconButton
                          icon="help-circle-outline"
                          iconColor="white"
                          onPress={() => {
                            // 点击帮助按钮时显示帮助对话框
                            setShowHelpDialog(true);
                          }}
                        />
                    </View>
                    <Text style={styles.stateText}>{getSheetTitle()}</Text>
                    <View style={styles.headerRight}>
                      <IconButton
                          icon="chevron-down"
                          iconColor="white"
                          onPress={handleClose}
                      />
                    </View>
                </View>

                <View style={styles.timerContainer}>
                    <Text style={styles.timerText}>{recordTime}</Text>
                </View>

                <View style={styles.controlsContainer}>
                    <View style={styles.sideButtonsLeft}>
                    {recordingState === "Recorded" && (
                        <Button
                        mode="text"
                        icon="delete"
                        textColor="white"
                        onPress={deleteRecording}
                        disabled={isLoading}
                        >
                        删除
                        </Button>
                    )}
                    {recordingState === "Play" && (
                        <Button
                        mode="text"
                        icon="text-box-search-outline"
                        textColor="white"
                        onPress={() => setShowTranscriptionDialog(true)}
                        disabled={isLoading}
                        >
                        识别结果
                        </Button>
                    )}
                    </View>

                    <View style={styles.centerButton}>
                    {renderRecordButton()}
                    {isLoading && (
                        <View style={styles.loadingContainer}>
                            <Text style={styles.loadingText}>Loading...</Text>
                        </View>
                    )}
                    </View>

                    <View style={styles.sideButtonsRight}>
                    {recordingState === "Recorded" && (
                        <Button
                        mode="text"
                        icon="upload"
                        textColor="white"
                        onPress={uploadRecording}
                        disabled={isLoading}
                        >
                        上传
                        </Button>
                    )}
                    {recordingState === "Play" && (
                        <Button
                        mode="text"
                        icon="microphone"
                        textColor="white"
                        onPress={() => setRecordingState("Record")}
                        disabled={isLoading}
                        >
                        重新录制
                        </Button>
                    )}
                    </View>
                </View>
            </BottomSheetView>
        </BottomSheetModal>

        <Portal>
            <Dialog visible={showUploadDialog} onDismiss={() => setShowUploadDialog(false)}>
            <Dialog.Title>上传成功</Dialog.Title>
            <Dialog.Content>
                <Text>录音已上传到服务器, 请耐心等待结果.</Text>
            </Dialog.Content>
            <Dialog.Actions>
                <Button onPress={handleUploadDialogConfirm}>确定</Button>
            </Dialog.Actions>
            </Dialog>
        </Portal>

        {/* 帮助对话框 */}
        <DialogToConfirm
            visible={showHelpDialog}
            title="提示"
            text={helperText}
            onOK={() => setShowHelpDialog(false)}
            okBtnLabel="知道了"
        />

        {/* 识别结果对话框 */}
        <Portal>
            <Dialog visible={showTranscriptionDialog} onDismiss={() => setShowTranscriptionDialog(false)}>
            <Dialog.Title>语音识别结果</Dialog.Title>
            <Dialog.Content>
                <Text>{transcribedText || "暂无识别结果"}</Text>
            </Dialog.Content>
            <Dialog.Actions>
                {/*<Button onPress={updateTranscribedTextCallback?.()}>更新</Button>*/}
                <Button onPress={() => setShowTranscriptionDialog(false)}>确定</Button>
            </Dialog.Actions>
            </Dialog>
        </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    backgroundColor: "transparent",
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flex: 1,
    alignItems: "flex-end",
  },
  stateText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    flex: 1,
  },
  timerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  timerText: {
    color: "white",
    fontSize: 48,
    fontWeight: "bold",
    fontFamily: "monospace",
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 40,
  },
  sideButtonsLeft: {
    flex: 1,
    alignItems: "flex-start",
  },
  centerButton: {
    flex: 1,
    alignItems: "center",
    position: "relative",
  },
  sideButtonsRight: {
    flex: 1,
    alignItems: "flex-end",
  },
  actionButton: {
    backgroundColor: "white",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  loadingContainer: {
    position: "absolute",
    marginTop: 10,
    top: 60,
    left: 0,
    right: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "white",
    fontSize: 12,
    opacity: 0.8,
  },
});

export default AudioRecorder;
